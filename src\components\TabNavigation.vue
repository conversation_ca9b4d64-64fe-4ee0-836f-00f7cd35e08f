<template>
  <div class="tab-navigation" v-if="showTabs">
    <div class="tab-container">
      <div
        v-for="(tab, index) in tabs"
        :key="tab.id"
        class="tab"
        :class="{ 'active': activeTab === tab.id }"
        @click="switchTab(tab.id)"
        @contextmenu.prevent="showContextMenu($event, tab, index)">
        {{ tab.title }}
        <button
          v-if="tab.closable"
          class="close-tab"
          @click.stop="closeTab(tab.id, index)">
          <span class="close-icon">×</span>
        </button>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="context-menu"
      :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
      @click.stop>
      <div class="context-menu-item" @click="closeCurrentTab">
        关闭
      </div>
      <div class="context-menu-item" @click="closeOtherTabs">
        关闭其他
      </div>
      <div class="context-menu-item" @click="closeAllTabs">
        关闭全部
      </div>
      <div class="context-menu-item" @click="closeTabsToRight">
        关闭右侧
      </div>
    </div>

    <!-- 点击遮罩关闭右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="context-menu-overlay"
      @click="hideContextMenu">
    </div>
    </div>
</template>

<script>
export default {
  name: 'TabNavigation',
  props: {
    // 当前激活的标签
    activeTab: {
      type: String,
      default: 'home'
    },
    // 是否显示标签导航
    showTabs: {
      type: Boolean,
      default: true
    },
    // 标签页数组
    tabs: {
      type: Array,
      default: () => [
        // { id: 'home', title: '首页', closable: false },
        { id: 'email', title: '邮件页', closable: false }
      ]
    }
  },
  data() {
    return {
      // 右键菜单相关
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      contextMenuTab: null,
      contextMenuIndex: -1
    }
  },
  methods: {
    switchTab(tabId) {
      // 发出事件通知父组件切换标签，父组件会处理路由跳转
      this.$emit('tab-change', tabId);
    },

    // 关闭标签页
    closeTab(tabId, index) {
      this.$emit('tab-close', { tabId, index });
    },

    // 显示右键菜单
    showContextMenu(event, tab, index) {
      if (!tab.closable && tab.id !== 'home' && tab.id !== 'email') {
        return; // 不可关闭的标签页不显示右键菜单
      }

      this.contextMenuVisible = true;
      this.contextMenuX = event.clientX;
      this.contextMenuY = event.clientY;
      this.contextMenuTab = tab;
      this.contextMenuIndex = index;

      // 添加全局点击监听器
      this.$nextTick(() => {
        document.addEventListener('click', this.hideContextMenu);
      });
    },

    // 隐藏右键菜单
    hideContextMenu() {
      this.contextMenuVisible = false;
      this.contextMenuTab = null;
      this.contextMenuIndex = -1;
      document.removeEventListener('click', this.hideContextMenu);
    },

    // 关闭当前标签页
    closeCurrentTab() {
      if (this.contextMenuTab && this.contextMenuTab.closable) {
        this.closeTab(this.contextMenuTab.id, this.contextMenuIndex);
      }
      this.hideContextMenu();
    },

    // 关闭其他标签页
    closeOtherTabs() {
      this.$emit('close-other-tabs', {
        keepTabId: this.contextMenuTab.id
      });
      this.hideContextMenu();
    },

    // 关闭全部标签页
    closeAllTabs() {
      this.$emit('close-all-tabs');
      this.hideContextMenu();
    },

    // 关闭右侧标签页
    closeTabsToRight() {
      this.$emit('close-tabs-to-right', {
        fromIndex: this.contextMenuIndex
      });
      this.hideContextMenu();
    }
  },

  beforeDestroy() {
    // 清理事件监听器
    document.removeEventListener('click', this.hideContextMenu);
  }
}
</script>

<style lang="scss" scoped>
.tab-navigation {
  width: 100%;
  background-color: #fff;
  border-bottom: 1px solid #e6e9ed;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1001 !important; /* 设置比全屏模式更高的z-index，确保在全屏模式下仍然可见 */
}

.tab-container {
  display: flex;
  max-width: 1200px;
  padding: 0 16px;
  overflow-x: auto;
  scrollbar-width: thin;
}

.tab-container::-webkit-scrollbar {
  height: 4px;
}

.tab-container::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 4px;
}

.tab {
  padding: 12px 24px;
  font-size: 14px;
  cursor: pointer;
  position: relative;
  color: #606266;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  white-space: nowrap;

  &:hover {
    color: #409EFF;
  }

  &.active {
    color: #409EFF;
    font-weight: 500;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: #409EFF;
    }
  }
}

.close-tab {
  margin-left: 8px;
  background: none;
  border: none;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  color: #909399;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #F56C6C;
  }
}

.close-icon {
  font-size: 14px;
  line-height: 1;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e6e9ed;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 120px;
  padding: 4px 0;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f7fa;
    color: #409EFF;
  }
}

.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
</style>
